import { RelicAgent } from 'relic-ui';

/**
 * Utility functions for handling organization-based permissions for agents
 */

export interface OrganizationPermissionContext {
  currentUserOrgId?: string;
  currentUserRole?: string;
  isAdmin?: boolean;
}

/**
 * Checks if the current user can edit/modify an agent based on organization permissions
 * @param agent - The agent to check permissions for
 * @param context - Current user's organization context
 * @returns true if user can edit the agent, false otherwise
 */
export const canEditAgent = (
  agent: RelicAgent | null | undefined,
  context: OrganizationPermissionContext
): boolean => {
  if (!agent || !context.currentUserOrgId) {
    return false;
  }

  // Admin users can edit all agents
  if (context.isAdmin || context.currentUserRole === 'admin') {
    return true;
  }

  // System agents cannot be edited by non-admin users
  if (agent.type === 'System Agent') {
    return false;
  }

  // Default agents (env === 'default') cannot be edited by non-admin users
  if (agent.env === 'default') {
    return false;
  }

  // Users can only edit agents from their own organization
  return agent.organizationId === context.currentUserOrgId;
};

/**
 * Checks if the current user can view an agent's details
 * @param agent - The agent to check permissions for
 * @param context - Current user's organization context
 * @returns true if user can view the agent details, false otherwise
 */
export const canViewAgent = (
  agent: RelicAgent | null | undefined,
  context: OrganizationPermissionContext
): boolean => {
  if (!agent) {
    return false;
  }

  // Admin users can view all agents
  if (context.isAdmin || context.currentUserRole === 'admin') {
    return true;
  }

  // All users can view agents from their own organization
  if (agent.organizationId === context.currentUserOrgId) {
    return true;
  }

  // Users can view default/system agents from other organizations (read-only)
  // This allows viewing Relic Care Org agents and other default assistants
  return agent.type === 'System Agent' || agent.env === 'default';
};

/**
 * Checks if an agent is read-only for the current user
 * @param agent - The agent to check
 * @param context - Current user's organization context
 * @returns true if agent is read-only, false if it can be edited
 */
export const isAgentReadOnly = (
  agent: RelicAgent | null | undefined,
  context: OrganizationPermissionContext
): boolean => {
  return canViewAgent(agent, context) && !canEditAgent(agent, context);
};

/**
 * Checks if the current user can delete/deactivate an agent
 * @param agent - The agent to check permissions for
 * @param context - Current user's organization context
 * @returns true if user can delete the agent, false otherwise
 */
export const canDeleteAgent = (
  agent: RelicAgent | null | undefined,
  context: OrganizationPermissionContext
): boolean => {
  if (!agent || !context.currentUserOrgId) {
    return false;
  }

  // System agents cannot be deleted
  if (agent.type === 'System Agent') {
    return false;
  }

  // Use same logic as edit permissions for delete
  return canEditAgent(agent, context);
};

/**
 * Checks if the current user can create new content (samples, templates) for an agent
 * @param agent - The agent to check permissions for
 * @param context - Current user's organization context
 * @returns true if user can create content for the agent, false otherwise
 */
export const canCreateAgentContent = (
  agent: RelicAgent | null | undefined,
  context: OrganizationPermissionContext
): boolean => {
  // Same logic as edit permissions
  return canEditAgent(agent, context);
};

/**
 * Gets the appropriate icon name for disabled/locked actions
 * @returns Material-UI icon name for lock
 */
export const getLockIconName = (): string => {
  return 'lock';
};

/**
 * Creates organization permission context from user data
 * @param currentUser - Current user object with organization and role info
 * @returns OrganizationPermissionContext object
 */
export const createPermissionContext = (currentUser: any): OrganizationPermissionContext => {
  return {
    currentUserOrgId: currentUser?.organizationId,
    currentUserRole: currentUser?.userIdentity?.role?.name,
    isAdmin: currentUser?.userIdentity?.role?.name === 'admin',
  };
};
