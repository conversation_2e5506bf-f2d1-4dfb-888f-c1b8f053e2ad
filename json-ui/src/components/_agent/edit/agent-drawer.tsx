import { useGetIdentity, useInvalidate, useTranslate } from '@refinedev/core';

import {
  Button,
  CircularProgress,
  Drawer,
  Icon<PERSON>utton,
  Stack,
} from '@mui/material';
import { useForm } from '@refinedev/react-hook-form';
import Iconify from 'src/components/iconify';

import { useAutocomplete } from '@refinedev/mui';
import React, { Dispatch, SetStateAction } from 'react';
import { FieldValues } from 'react-hook-form';
import { Edit } from 'src/components/refine-customs/edit';
import { ReturnType } from 'src/hooks/use-popover';
import AgentDetailsView from './agent-details-view';
import AgentEditForm from './agent-edit-form';

import { IUser, RelicAgent } from 'relic-ui';

const DRAWER_WIDTH = {
  lg: 400,
  md: 350,
  sm: 300,
  xs: '100%',
};
const AgentDrawer = ({
  showAgent,
  showEdit,
  setShowEdit,
  popoverDetails,
  agentData,
  loading,
}: {
  showAgent: boolean;
  showEdit: boolean;
  setShowEdit: Dispatch<SetStateAction<boolean>>;
  popoverDetails: ReturnType;
  agentData: RelicAgent;
  loading: boolean;
}) => {
  const translate = useTranslate();
  const invalidate = useInvalidate();

  // Get current user for permission context
  const { data: currentUser } = useGetIdentity<IUser>();

  // Check if agent is read-only based on organization
  const isReadOnly = React.useMemo(() => {
    if (!agentData || !currentUser) return false;
    const isAdmin = currentUser?.userIdentity?.role?.name === 'admin';
    return agentData.organizationId !== currentUser.organizationId && !isAdmin;
  }, [agentData, currentUser]);

  // Initialize useUpdate hook for agent mutation

  const agentForm = useForm<RelicAgent>({
    defaultValues: agentData,
    refineCoreProps: {
      resource: 'agents',
      id: agentData?.id,
      action: 'edit',
      redirect: false,
    },
  });

  const {
    handleSubmit,
    formState: { isSubmitting },
    refineCore: { onFinish },
  } = agentForm;

  // Submit handler for agent update
  const onSubmit = async (values: FieldValues) => {
    try {
      // stopSequences is an array of strings, but the form is sending an empty string when the field is empty
      agentData.azureAssistantSetup.chatParameters.stopSequences =
        values.azureAssistantSetup.chatParameters.stopSequences || [];
      const updateValues = {
        ...agentData,
        ...values,
        enabled: values.active,
      };

      await onFinish(updateValues);

      invalidate({
        resource: 'agents',
        invalidates: ['list', 'many', 'detail'],
        id: agentData?.id,
      });

      setShowEdit(false);
    } catch (error) {
      console.error('Failed to update agent:', error);
    }
  };

  const { autocompleteProps: organizationAutocompleteProps } = useAutocomplete({
    resource: 'organizations',
    defaultValue: agentData?.organizationId,
  });

  return (
    <Drawer
      anchor="right"
      variant="persistent"
      open={showAgent}
      PaperProps={{
        sx: {
          zIndex: 1,
          top: 8 * 18.5,
          boxSizing: 'border-box',
          width: {
            xs: DRAWER_WIDTH.xs,
            sm: DRAWER_WIDTH.sm,
            md: DRAWER_WIDTH.md,
            lg: DRAWER_WIDTH.lg,
          },
        },
      }}
      sx={{
        flexShrink: 0,
        width: {
          xs: DRAWER_WIDTH.xs,
          sm: DRAWER_WIDTH.sm,
          md: DRAWER_WIDTH.md,
          lg: DRAWER_WIDTH.lg,
        },
      }}
    >
      <Edit
        saveButtonProps={{
          disabled: isSubmitting,
          loading: isSubmitting,
          onClick: handleSubmit(onSubmit),
        }}
        breadcrumb={null}
        goBack={null}
        wrapperProps={{
          sx: {
            flexGrow: 1,
            borderRadius: 0,
            boxShadow: 'none',
            overflowY: 'auto',
            mb: `${8 * 18.5}px`,
          },
        }}
        headerButtonProps={{
          sx: { display: 'none' },
        }}
        headerProps={{
          title: translate('agents.details'),
          action: !loading && (
            <>
              {showEdit ? (
                <Button
                  variant="outlined"
                  startIcon={<Iconify icon={'eva:close-fill'} />}
                  onClick={() => setShowEdit(false)}
                >
                  Cancel
                </Button>
              ) : (
                // Only show popover menu if agent is not read-only
                !isReadOnly && (
                  <IconButton onClick={popoverDetails.onOpen}>
                    <Iconify icon="eva:more-vertical-fill" />
                  </IconButton>
                )
              )}
            </>
          ),
        }}
        footerButtonProps={{
          ...((!showEdit || isReadOnly) && {
            sx: { display: 'none' },
          }),
        }}
        deleteButtonProps={{
          hidden: true,
        }}
      >
        {loading ? (
          <Stack
            sx={{ width: 1, height: '62vh' }}
            alignItems="center"
            justifyContent="center"
          >
            <CircularProgress color="inherit" />
          </Stack>
        ) : (
          <>
            {showEdit && !isReadOnly ? (
              <AgentEditForm
                showEdit={showEdit}
                agentData={agentData}
                agentForm={agentForm}
              />
            ) : (
              <AgentDetailsView
                showEdit={showEdit}
                agentsData={agentData}
                organizationAutocompleteProps={organizationAutocompleteProps}
              />
            )}
          </>
        )}
      </Edit>
    </Drawer>
  );
};

export default AgentDrawer;
