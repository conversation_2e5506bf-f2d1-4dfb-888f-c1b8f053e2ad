import { useGetIdentity, useTranslate } from '@refinedev/core';
import { useAutocomplete } from '@refinedev/mui';
import { useMemo } from 'react';
import { IUser, RelicOrganization } from 'relic-ui';

import { Box, Stack, Typography } from '@mui/material';

import Label from 'src/components/label';

interface AgentDetailsViewProps {
  showEdit: boolean;
  agentsData: any;
  organizationAutocompleteProps: any;
}

const AgentDetailsView = ({
  showEdit,
  agentsData,
  organizationAutocompleteProps,
}: AgentDetailsViewProps) => {
  const translate = useTranslate();

  // Get current user for permission context
  const { data: currentUser } = useGetIdentity<IUser>();
  const permissionContext = useMemo(() => {
    return {
      currentUserOrgId: currentUser?.organizationId,
      currentUserRole: currentUser?.userIdentity?.role?.name,
      isAdmin: currentUser?.userIdentity?.role?.name === 'admin',
    };
  }, [currentUser]);

  const isReadOnly = useMemo(() => {
    if (!agentsData) return false;
    // Agent is read-only if it's from a different organization (unless admin)
    return (
      agentsData.organizationId !== permissionContext.currentUserOrgId &&
      !permissionContext.isAdmin
    );
  }, [agentsData, permissionContext]);

  const currentOrganization: RelicOrganization = useMemo(() => {
    return organizationAutocompleteProps?.options?.find(
      (org: RelicOrganization) => org.id === agentsData?.organizationId,
    );
  }, [organizationAutocompleteProps?.options, agentsData?.organizationId]);

  const { autocompleteProps: trainingAutocompleteProps } = useAutocomplete({
    resource: 'trainings/',
  });

  const training = useMemo(() => {
    return trainingAutocompleteProps?.options?.find(
      (training: any) =>
        training.indexName === agentsData?.relicAssistantSetup?.kbLinked[0],
    );
  }, [
    trainingAutocompleteProps?.options,
    agentsData?.relicAssistantSetup?.kbLinked,
  ]);

  return (
    <>
      <Stack
        spacing={2}
        component={'form'}
        sx={{
          flexDirection: 'column',
          display: showEdit ? 'none' : 'flex',
        }}
      >
        {/* Read-only indicator for cross-organization agents */}
        {isReadOnly && (
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="caption"
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
                color: 'text.secondary',
                backgroundColor: 'grey.100',
                padding: 1,
                borderRadius: 1,
              }}
            >
              🔒
              {translate(
                'agents.readOnlyMessage',
                'This agent is from another organization and is read-only',
              )}
            </Typography>
          </Box>
        )}
        <Box>
          <Typography
            component={'span'}
            variant="subtitle2"
            sx={{
              fontSize: 12,
              color: 'text.secondary',
              display: 'block',
              mb: 0.5,
            }}
          >
            {translate('agents.fields.name')}
          </Typography>

          <Typography component={'span'} variant="body2">
            {agentsData?.name || '-'}
          </Typography>
        </Box>
        <Box>
          <Typography
            component={'span'}
            variant="subtitle2"
            sx={{
              fontSize: 12,
              color: 'text.secondary',
              display: 'block',
              mb: 0.5,
            }}
          >
            {translate('agents.fields.role')}
          </Typography>
          <Typography component={'span'} variant="body2">
            {agentsData?.role['display-role'] || '-'}
          </Typography>
        </Box>
        <Box>
          <Typography
            component={'span'}
            variant="subtitle2"
            sx={{
              fontSize: 12,
              color: 'text.secondary',
              display: 'block',
              mb: 0.5,
            }}
          >
            {translate('agents.fields.organizationId')}
          </Typography>
          <Typography component={'span'} variant="body2">
            {currentOrganization?.name || '-'}
          </Typography>
        </Box>
        <Box>
          <Typography
            component={'span'}
            variant="subtitle2"
            sx={{
              fontSize: 12,
              color: 'text.secondary',
              display: 'block',
              mb: 0.5,
            }}
          >
            {translate('agents.fields.training')}
          </Typography>
          <Typography component={'span'} variant="body2">
            {training?.name || '-'}
          </Typography>
        </Box>
        <Box>
          <Typography
            component={'span'}
            variant="subtitle2"
            sx={{
              fontSize: 12,
              color: 'text.secondary',
              display: 'block',
              mb: 0.5,
            }}
          >
            {translate('agents.fields.bio')}
          </Typography>
          <Typography component={'span'} variant="body2">
            {agentsData?.publicData?.bio || '-'}
          </Typography>
        </Box>
        <Box>
          <Typography
            component={'span'}
            variant="subtitle2"
            sx={{
              fontSize: 12,
              color: 'text.secondary',
              display: 'block',
              mb: 0.5,
            }}
          >
            {translate('agents.fields.avatarUrl')}
          </Typography>
          <Typography component={'span'} variant="body2">
            {agentsData?.publicData?.avatarUrl || '-'}
          </Typography>
        </Box>
        <Box>
          <Typography
            component={'span'}
            variant="subtitle2"
            sx={{
              fontSize: 12,
              color: 'text.secondary',
              display: 'block',
              mb: 0.5,
            }}
          >
            {translate('agents.fields.coverUrl')}
          </Typography>
          <Typography component={'span'} variant="body2">
            {agentsData?.publicData?.coverUrl || '-'}
          </Typography>
        </Box>
        <Box>
          <Typography
            component={'span'}
            variant="subtitle2"
            sx={{
              fontSize: 12,
              color: 'text.secondary',
              display: 'block',
              mb: 0.5,
            }}
          >
            {translate('agents.fields.active')}
          </Typography>
          <Typography component={'span'} variant="body2">
            {agentsData?.active ? (
              <Label
                variant="soft"
                color={agentsData?.active ? 'success' : 'error'}
              >
                {translate(
                  agentsData?.active ? 'status.active' : 'status.inactive',
                )}
              </Label>
            ) : (
              '-'
            )}
          </Typography>
        </Box>
        <Typography variant="body1" fontWeight="bold">
          {translate('agents.setup')}
        </Typography>
        <Box>
          <Typography
            component={'span'}
            variant="subtitle2"
            sx={{
              fontSize: 12,
              color: 'text.secondary',
              display: 'block',
              mb: 0.5,
            }}
          >
            {translate('agents.fields.maxResponseLength')}
          </Typography>
          <Typography component={'span'} variant="body2">
            {agentsData?.azureAssistantSetup?.chatParameters
              ?.maxResponseLength || '-'}
          </Typography>
        </Box>
        <Box>
          <Typography
            component={'span'}
            variant="subtitle2"
            sx={{
              fontSize: 12,
              color: 'text.secondary',
              display: 'block',
              mb: 0.5,
            }}
          >
            {translate('agents.fields.temperature')}
          </Typography>
          <Typography component={'span'} variant="body2">
            {agentsData?.azureAssistantSetup?.chatParameters?.temperature ||
              '-'}
          </Typography>
        </Box>
        <Box>
          <Typography
            component={'span'}
            variant="subtitle2"
            sx={{
              fontSize: 12,
              color: 'text.secondary',
              display: 'block',
              mb: 0.5,
            }}
          >
            {translate('agents.fields.topProbabilities')}
          </Typography>
          <Typography component={'span'} variant="body2">
            {agentsData?.azureAssistantSetup?.chatParameters
              ?.topProbabilities || '-'}
          </Typography>
        </Box>
        <Box>
          <Typography
            component={'span'}
            variant="subtitle2"
            sx={{
              fontSize: 12,
              color: 'text.secondary',
              display: 'block',
              mb: 0.5,
            }}
          >
            {translate('agents.fields.stopSequences')}
          </Typography>
          <Typography component={'span'} variant="body2">
            {agentsData?.azureAssistantSetup?.chatParameters?.stopSequences ||
              '-'}
          </Typography>
        </Box>
        <Box>
          <Typography
            component={'span'}
            variant="subtitle2"
            sx={{
              fontSize: 12,
              color: 'text.secondary',
              display: 'block',
              mb: 0.5,
            }}
          >
            {translate('agents.fields.frequencyPenalty')}
          </Typography>
          <Typography component={'span'} variant="body2">
            {agentsData?.azureAssistantSetup?.chatParameters
              ?.frequencyPenalty || '-'}
          </Typography>
        </Box>
        <Box>
          <Typography
            component={'span'}
            variant="subtitle2"
            sx={{
              fontSize: 12,
              color: 'text.secondary',
              display: 'block',
              mb: 0.5,
            }}
          >
            {translate('agents.fields.presencePenalty')}
          </Typography>
          <Typography component={'span'} variant="body2">
            {agentsData?.azureAssistantSetup?.chatParameters?.presencePenalty ||
              '-'}
          </Typography>
        </Box>
        {/* 
        // NOTE:  This part is commented out because will be coming in the future with different approach
        <Box>
          <Typography
            component={'span'}
            variant="subtitle2"
            sx={{
              fontSize: 12,
              color: 'text.secondary',
              display: 'block',
              mb: 0.5,
            }}
          >
            {translate('agents.fields.responseFrom')}
          </Typography>
          <Typography component={'span'} variant="body2">
            {agentsData?.relicAssistantSetup?.responseFrom
              ? translate('agents.fields.openAi')
              : translate('agents.fields.training')}
          </Typography>
        </Box> */}
      </Stack>
    </>
  );
};

export default AgentDetailsView;
